import 'package:built_collection/built_collection.dart';
import 'package:best_practices/best_practices.dart';
import 'package:cable_flutter/cable_flutter.dart';
import 'package:common/common.dart';
import 'package:common/local_event.dart';
import 'package:design_token/design_token.dart';
import 'package:flutter/material.dart';
import 'package:galileo_flutter/galileo_page.dart';
import 'package:insurance/src/common/model/channel_product_model.dart';
import 'package:insurance/src/common/widgets/card_base_component.dart';
import 'package:insurance/src/common/widgets/multiple_activities_card.dart';
import 'package:insurance/src/l10n/common_localizations.dart';
import 'package:insurance/src/main_component/model/all_traveller_list_event.dart';
import 'package:insurance/src/main_component/model/main_compoent_event.dart';
import 'package:insurance/src/main_component/model/main_component_model.dart';
import 'package:insurance/src/multiple_activities_upgrades/bloc/multiple_activities_upgrades_bloc.dart';
import 'package:insurance/src/multiple_activities_upgrades/bloc/multiple_activities_upgrades_bloc_state.dart';
import 'package:insurance/src/multiple_activities_upgrades/data/multiple_activities_repository.dart';
import 'package:insurance/src/pa/utils/add_pa_util.dart';
import 'package:insurance/src/policy_holder/utils/add_cruise_util.dart';
import 'package:insurance/src/traveller/list/model/traveller_list_model.dart';
import 'package:kltracker/kltracker.dart';
import 'package:l10n_cms/l10n_cms.dart';
import 'package:ok_route/ok_route.dart';
import 'package:ok_route_annotation/ok_route_annotation.dart';
import 'package:provider/provider.dart';
import 'package:ui_component/button.dart';
import 'package:ui_component/common_app_bar.dart';

@OKRoute(
    routeName: "klook-flutter://insurance/multipleActivitiesUpgrades",
    name: 'multipleActivitiesUpgrades',
    description: 'Multiple Activities Upgrades Page')
class MultipleActivitiesUpgradesPageRouteEntry extends DefaultRouteEntry {
  MultipleActivitiesUpgradesPageRouteEntry(String routeName, Object? arguments)
      : super(routeName: routeName, arguments: arguments);

  @override
  Widget build(BuildContext context) {
    BuiltList<ChannelProductModel> channelProductList = BuiltList();
    BuiltList<TravellerInfoModel> allTravellerInfoModelList = BuiltList();
    String currencySymbol = "";
    String zaToken = "";
    String currency = "";
    bool isSelectTakeRisk = false;
    bool isMandatoryOptionsTest = false;
    Map<String, bool> optInStatus = <String, bool>{};
    Map<String, bool> optInDefaultFlagMap = <String, bool>{};
    final argumentsMap = arguments;
    if (argumentsMap is Map) {
      channelProductList = (argumentsMap["channel_product_list"]
              as BuiltList<ChannelProductModel>?) ??
          BuiltList();
      currencySymbol = (argumentsMap["currency_symbol"] as String?) ?? "";

      allTravellerInfoModelList = (argumentsMap["all_traveller_ist"]
              as BuiltList<TravellerInfoModel>?) ??
          BuiltList();
      zaToken = (argumentsMap["zaToken"] as String?) ?? "";
      currency = (argumentsMap["currency"] as String?) ?? "";
      isSelectTakeRisk = (argumentsMap["isSelectTakeRisk"] as bool?) ?? false;
      isMandatoryOptionsTest =
          (argumentsMap["isMandatoryOptionsTest"] as bool?) ?? false;
      optInStatus =
          typeValue<Map<String, bool>>(argumentsMap["opt_in_status"]) ?? {};
      optInDefaultFlagMap =
          typeValue<Map<String, bool>>(argumentsMap["opt_in_default_flag"]) ??
              {};
    }
    return BlocProvider(
      create: (context) => MultipleActivitiesUpgradesBloc(
        channelProductList: channelProductList,
        allTravellerInfoModelList: allTravellerInfoModelList,
        zaToken: zaToken,
        currency: currency,
        currencySymbol: currencySymbol,
        isShowSelectHint: false,
        isSelectTakeRisk: isSelectTakeRisk,
        optInStatusMap: optInStatus,
        optInDefaultFlagMap: optInDefaultFlagMap,
        isMandatoryOptionsTest: isMandatoryOptionsTest,
        repository: MultipleActivitiesRepository(
          Provider.of<CommonRequest>(context, listen: false),
          Provider.of<Serialization>(context, listen: false),
        ),
      ),
      child: GalileoPage(
        spm: 'InsuranceActivityList',
        child: TrackingWidget.page(
          spmName: 'InsuranceActivityList',
          child: const MultipleActivitiesUpgradesPage(),
        ),
      ),
    );
  }
}

class MultipleActivitiesUpgradesPage extends StatefulWidget
    with LocalEventSenderMixin {
  const MultipleActivitiesUpgradesPage({super.key});

  @override
  _MultipleActivitiesUpgradesStatePage createState() =>
      _MultipleActivitiesUpgradesStatePage();
}

class _MultipleActivitiesUpgradesStatePage
    extends State<MultipleActivitiesUpgradesPage> with LocalEventReceiverMixin {
  late MultipleActivitiesUpgradesBloc _bloc;

  @override
  void initState() {
    super.initState();
    onEventReceived<AllTravellerListEvent>((event) {
      _bloc.updateTravellerList(event.allTravellerList);
    });
    _bloc = Provider.of<MultipleActivitiesUpgradesBloc>(context, listen: false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.colorBgWidgetDarker3,
      appBar: CommonAppBar(
        title: L10NText(
          context.localizations().l10n_normal_13554(),
        ),
      ),
      body: StreamBuilder<MultipleActivitiesUpgradesBlocState>(
        stream: _bloc.stream,
        builder: (context, snapshot) {
          final state = snapshot.data;
          if (state == null) {
            return Container();
          }
          return CustomScrollView(
            physics: const ClampingScrollPhysics(),
            slivers: [
              ...state.channelProductList.map((channelProductModel) {
                return SliverPadding(
                  padding: const EdgeInsets.only(top: 12),
                  sliver: SliverToBoxAdapter(
                    child: MultipleActivityEditCard(
                      isMandatoryOptionsTest: state.isMandatoryOptionsTest,
                      showHintFlag: state.isShowSelectHint,
                      channelProductModel: channelProductModel,
                      currencySymbol: state.currencySymbol,
                      optInDefaultFlagMap: state.optInDefaultFlagMap,
                      onSelectedChange:
                          (channelProductModel, insuranceProductModel) {
                        selectItemChangedProcess(
                            channelProductModel, insuranceProductModel);
                        _bloc.updateTakeRiskStatus(false);
                        _bloc.updateShowSelectHint(false);
                      },
                      onZurichEditProcess:
                          (channelProductModel, insuranceProductModel) async {
                        // 点击编辑Zurich
                        var travelInsuranceResult =
                            await editTravelInsurancePolicy(context,
                                channelProductModel: channelProductModel,
                                insuranceProductModel: insuranceProductModel,
                                oldTravellerList:
                                    state.allTravellerInfoModelList);
                        if (travelInsuranceResult == null) {
                          return;
                        }
                        // 更新试算
                        _bloc.updateSingleGoodsQuotation(
                            channelProductModel, travelInsuranceResult);
                        if (travelInsuranceResult.startTime.toString() !=
                                insuranceProductModel.startTime.toString() ||
                            travelInsuranceResult.endTime.toString() !=
                                insuranceProductModel.endTime.toString()) {
                          travelInsuranceResult = travelInsuranceResult
                              .rebuild((p0) => p0..updateQuotation = true);
                        }
                        _bloc.editSelectedTravellerList(
                            channelProductModel.itemGuid,
                            travelInsuranceResult);
                      },
                      onCruiseEditProcess:
                          (channelProductModel, insuranceProductModel) async {
                        // 多活动里层页面，手动取消警告弹窗
                        _bloc.updateOtherInfoChangeFlag(insuranceProductModel,
                            channelProductModel.itemGuid, false);
                        // 点击编辑邮轮投、被保人信息
                        final cruiseResult = await editCruiseInsuranceProduct(
                          context,
                          zaToken: state.zaToken,
                          channelProductModel: channelProductModel,
                          insuranceProductModel: insuranceProductModel.rebuild(
                              (p0) => p0..isShowOtherInfoChangeDesc = false),
                          oldTravellerList: state.allTravellerInfoModelList,
                        );
                        if (cruiseResult != null) {
                          _bloc.editSelectedTravellerList(
                              channelProductModel.itemGuid, cruiseResult);
                        }
                      },
                      onOptInStatusChanged: (goodsCode, planCode, status) {
                        _bloc.requestUpdateOptInSelectStatus(
                            goodsCode, planCode, status);
                      },
                      isOptInSelected: _bloc.state.optInStatusMap,
                      onEditCoverage:
                          (channelProductModel, insuranceProductModel) async {
                        if (insuranceProductModel.inpathInsuranceType ==
                            InpathInsuranceType.pa) {
                          final paResult = await selectedTravellers(
                            context,
                            zaToken: state.zaToken,
                            currency: state.currency,
                            channelProductModel: channelProductModel,
                            insuranceProductModel: insuranceProductModel,
                            allTravellerInfoModelsGetter: () =>
                                _bloc.state.allTravellerInfoModelList,
                          );
                          if (paResult != null) {
                            _bloc.editSelectedTravellerList(
                                channelProductModel.itemGuid, paResult);
                          }
                        }
                      },
                      onEditPolicyholder: (_, insuranceProductModel) async {
                        final paResult = await editPolicyholder(context,
                            channelProductModel, insuranceProductModel);
                        if (paResult != null) {
                          _bloc.editSelectedTravellerList(
                              channelProductModel.itemGuid, paResult);
                        }
                      },
                      onSelectedNotifyChanged: (_, insuranceProductModel) {
                        _bloc.updateProductNotify(channelProductModel.itemGuid,
                            insuranceProductModel);
                        widget.localEventSender.sendEvent(
                          ChannelProductNotifyChangeEvent((b) => b
                            ..itemGuid = channelProductModel.itemGuid
                            ..insuranceProductModel =
                                insuranceProductModel.toBuilder()),
                        );
                      },
                    ),
                  ),
                );
              }),
              if (state.isMandatoryOptionsTest)
                SliverToBoxAdapter(
                  child: Container(
                    padding: const EdgeInsets.only(top: 12),
                    child: InsuranceMandatoryOptionWithBorder(
                        takeRisk: _bloc.state.isSelectTakeRisk,
                        onChanged: (value) {
                          if (value) {
                            clearSelectedItemStatus(
                                _bloc.state.channelProductList);
                          }
                          _bloc.updateTakeRiskStatus(value);
                          _bloc.updateShowSelectHint(false);
                        },
                        showHintFlag: _bloc.state.isShowSelectHint),
                  ),
                ),
              if (state.channelProductList
                  .any((e) => e.isSelectedInsuranceProduct()))
                SliverToBoxAdapter(
                  child: _buildTnc(),
                ),
              SliverFillRemaining(
                hasScrollBody: false,
                child: Container(
                  padding: const EdgeInsets.only(top: 12),
                  alignment: Alignment.bottomCenter,
                  child: TrackingWidget.module(
                    spmName: 'Save',
                    otherInfoBuilder: (context) => {
                      // 用户看到的保险试算ID，list格式
                      'DisplayQuotationID': state.channelProductList
                          .map((e) => e.insuranceProductList.map(
                              (product) => product.premiumModel.quotationId))
                          .expand((element) => element)
                          .toList(),
                      // 用户购买的保险试算ID，list格式
                      'BuyQuotationID': state.channelProductList
                          .map((e) => e.insuranceProductList
                              .where((product) =>
                                  product.selected && !product.isVirtualType())
                              .map((product) =>
                                  product.premiumModel.quotationId))
                          .expand((element) => element)
                          .toList(),
                    },
                    child: Builder(
                      builder: (context) => Container(
                        color: Theme.of(context).colorScheme.colorCommonWhite,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 16,
                        ),
                        child: Button(
                          width: double.infinity,
                          appearance: const ButtonAppearance(
                            type: ButtonType.primary,
                          ),
                          text: L10NText(
                              context.localizations().l10n_normal_30209()),
                          onPressed: () {
                            if (state.isMandatoryOptionsTest &&
                                !state.channelProductList.any(
                                    (e) => e.isSelectedInsuranceProduct()) &&
                                !state.isSelectTakeRisk) {
                              _bloc.updateShowSelectHint(true);
                              return;
                            }
                            trackingWrapper(
                              context,
                              () => Provider.of<Cable>(context, listen: false)
                                  .pop({
                                "channel_product_list":
                                    state.channelProductList,
                                "select_take_risk": state.isSelectTakeRisk,
                                "opt_in_status": state.optInStatusMap,
                                "opt_in_default_flag":
                                    state.optInDefaultFlagMap,
                              }),
                            ).call();
                          },
                        ),
                      ),
                    ),
                  ),
                ),
              )
            ],
          );
        },
      ),
    );
  }

  Future<void> selectItemChangedProcess(ChannelProductModel channelProductModel,
      InsuranceProductModel insuranceProductModel) async {
    if (insuranceProductModel.inpathInsuranceType == InpathInsuranceType.pa &&
        !insuranceProductModel.selected) {
      widget.localEventSender
          .sendEvent(BatchGoodsQuotationEvent((update) => update
            ..channelProductModel = channelProductModel.toBuilder()
            ..insuranceProductModel = insuranceProductModel.toBuilder()));
      final paResult = await editPaInsuranceProduct(
        context,
        zaToken: _bloc.state.zaToken,
        currency: _bloc.state.currency,
        allTravellerInfoModelsGetter: () =>
            _bloc.state.allTravellerInfoModelList,
        channelProductModel: channelProductModel,
        insuranceProductModel: insuranceProductModel,
      );
      if (paResult is InsuranceProductModel) {
        _bloc.updateSelectedList(channelProductModel.itemGuid, paResult);
      }
    } else if (insuranceProductModel.inpathInsuranceType ==
            InpathInsuranceType.cruise &&
        !insuranceProductModel.selected) {
      _bloc.updateOtherInfoChangeFlag(
          insuranceProductModel, channelProductModel.itemGuid, false);
      // 勾选邮轮险
      final cruiseResult = await editCruiseInsuranceProduct(
        context,
        zaToken: _bloc.state.zaToken,
        channelProductModel: channelProductModel,
        insuranceProductModel: insuranceProductModel
            .rebuild((p0) => p0..isShowOtherInfoChangeDesc = false),
        oldTravellerList: _bloc.state.allTravellerInfoModelList,
      );
      if (cruiseResult != null) {
        _bloc.updateSelectedList(channelProductModel.itemGuid, cruiseResult);
      }
    } else if (InpathInsuranceGoodsCode.isZurichSg(
            insuranceProductModel.getGoodsCode()) &&
        !insuranceProductModel.selected) {
      final factorTime = FactorMapModel.fromJson(
          channelProductModel.channelProductInfo.factorMap.asMap);
      var newInsuranceModel = insuranceProductModel;
      if (newInsuranceModel.startTime == null) {
        newInsuranceModel = newInsuranceModel.rebuild((p0) => p0
          ..startTime = factorTime?.getUtcTime()
          ..endTime = factorTime?.getDefaultExpireDate());
      }
      newInsuranceModel = newInsuranceModel.rebuild((p0) => p0
        ..firstName = channelProductModel.channelProductInfo.contactUserInfo
                ?.firstOrNull?.formInfoList?.firstOrNull
                ?.getContactFirstName() ??
            ""
        ..lastName = channelProductModel.channelProductInfo.contactUserInfo
                ?.firstOrNull?.formInfoList?.firstOrNull
                ?.getContactLastName() ??
            "");
      final travelInsuranceResult = await editTravelInsurancePolicy(context,
          channelProductModel: channelProductModel,
          insuranceProductModel: newInsuranceModel,
          oldTravellerList: _bloc.state.allTravellerInfoModelList);
      if (travelInsuranceResult != null) {
        _bloc.updateSelectedList(
            channelProductModel.itemGuid, travelInsuranceResult);
      }
    } else {
      _bloc.updateSelectedList(
          channelProductModel.itemGuid, insuranceProductModel);
    }
  }

  void clearSelectedItemStatus(BuiltList<ChannelProductModel> channelList) {
    for (final ChannelProductModel channelProduct in channelList) {
      for (final InsuranceProductModel insuranceProduct
          in channelProduct.insuranceProductList) {
        if (insuranceProduct.selected) {
          selectItemChangedProcess(channelProduct, insuranceProduct);
        }
      }
    }
  }

  Widget _buildTnc() {
    final isContainPA = _bloc.state.channelProductList
        .expand((e) => e.insuranceProductList)
        .any(
            (element) => element.inpathInsuranceType == InpathInsuranceType.pa);
    return Container(
      color: Theme.of(context).colorScheme.colorCommonWhite,
      margin: const EdgeInsets.only(top: 12),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: InsuranceTNC(
        isContainPA: isContainPA,
      ),
    );
  }
}

import 'package:built_collection/built_collection.dart';
import 'package:dio/dio.dart';
import 'package:built_value/json_object.dart';
import 'package:common/common.dart';
import 'package:insurance/src/common/model/channel_product_model.dart';
import 'package:insurance/src/main_component/model/main_component_model.dart';
import 'package:insurance/src/policy_holder/model/policy_holder_model.dart';

class MainComponentRepository extends BaseBlocRepository {
  MainComponentRepository(this._commonRequest, this._serialization,
      {CompositeCancellable? compositeCancellable})
      : _compositeCancellable = compositeCancellable ?? CompositeCancellable();
  final CommonRequest _commonRequest;
  final CompositeCancellable _compositeCancellable;
  final Serialization _serialization;

  Future<FusionLoginInfo?> requestFusionToken() {
    return _commonRequest
        .get<FusionLoginInfo>('/v1/insuranceapisrv/outer/fusion/token')
        .addToCompositeCancellable(_compositeCancellable)
        .asFuture();
  }

  Future<BuiltList<GoodsAvailableModel>> requestGoodsAvailable(
      EntranceConfigModel model, String? zaToken) {
    return _commonRequest
        .post<JsonObject>('/v1/insuranceapisrv/outer/fusion/goods/available',
            data: {
              'country_code': model.countryCode,
              'rule_code_list': model.promotionTags.toList(),
              'channel_product_list': model.channelProductList
                  .map((product) => {
                        'sub_category_id':
                            product.channelProductInfo.subCategoryId,
                        'channel_product_id': product.channelProductId,
                        'item_guid': product.itemGuid,
                        'main_activity_raw_factor_map':
                            product.channelProductInfo.factorMap.asMap,
                        'booking_value': product.bookingValue
                      })
                  .toList(),
              'currency': model.currency
            },
            requestSerializerType: SerializerType.JSON,
            extraOptions: Options(headers: {'x-za-token': zaToken}))
        .addToCompositeCancellable(_compositeCancellable)
        .asFuture()
        .catchError(( error) => throw error)
        .then((value) => (value != null)
            ? _serialization.deserializeListOf<GoodsAvailableModel>(
                List<Map<String, dynamic>>.from(value.asList))
            : BuiltList());
  }

  Future<BuiltList<GoodsQuotationModel>> requestGoodsQuotation(String currency,
      String? zaToken, List<Map<String, Object?>> channelProductList) {
    return _commonRequest
        .post<JsonObject>('/v1/insuranceapisrv/outer/premium/quotation',
            data: {
              'currency': currency,
              'channel_product_list': channelProductList
            },
            requestSerializerType: SerializerType.JSON,
            extraOptions: Options(headers: {'x-za-token': zaToken}))
        .addToCompositeCancellable(_compositeCancellable)
        .asFuture()
        .catchError((error) => throw error)
        .then((value) => (value != null)
            ? _serialization.deserializeListOf<GoodsQuotationModel>(
                List<Map<String, dynamic>>.from(value.asList))
            : BuiltList());
  }

  Future<BuiltList<InPathProductInfo>> requestInsuranceProductInfo(
      Map<String, Object?> requestData) {
    return _commonRequest
        .post<JsonObject>(
          '/v1/insuranceapisrv/outer/inpath/product/info',
          data: requestData,
          requestSerializerType: SerializerType.JSON,
        )
        .addToCompositeCancellable(_compositeCancellable)
        .asFuture()
        .catchError((error) => throw error)
        .then((value) => (value != null)
            ? _serialization.deserializeListOf<InPathProductInfo>(
                List<Map<String, dynamic>>.from(value.asList))
            : BuiltList());
  }

  /// originOtherInfo --- 将当前otherInfo的关键信息上报，用于后端进行比较是否发生变更
  /// 后端判断若originOtherInfo和最新的otherInfo不一致，则会修改前端产品展示提示卡片的状态为true
  Future<PolicyHolderModel?> requestCruisePolicyHolder(
      String goodsCode,
      String planCode,
      Map? map,
      BuiltList<JsonObject>? otherInfo,
      List<Map<String, String>> localUserInfo,
      List<Map<String, String>>? originOtherInfo,
      String? zaToken) {
    return _commonRequest
        .post<PolicyHolderModel>(
          '/v1/insuranceapisrv/outer/policy/holder/cruise',
          data: {
            "goods_code": goodsCode,
            "plan_code": planCode,
            "data": map,
            "section_other_info":
                otherInfo?.map((p0) => (p0 as MapJsonObject).asMap).toList(),
            "local_user_info": localUserInfo,
            "origin_other_info": originOtherInfo,
          },
          requestSerializerType: SerializerType.JSON,
          extraOptions: Options(headers: {'x-za-token': zaToken}),
        )
        .addToCompositeCancellable(_compositeCancellable)
        .asFuture();
  }

  void requestUpdateOptInInfo(
      String goodsCode, String planCode, bool status, String? zaToken) {
    _commonRequest
        .post<JsonObject>(
          '/v1/insuranceapisrv/outer/experiment/goods/optIn/save',
          data: {
            "goods_code": goodsCode,
            "plan_code": planCode,
            "opt_in_status": status ? 1 : 0
          },
          requestSerializerType: SerializerType.JSON,
          extraOptions: Options(headers: {'x-za-token': zaToken}),
        )
        .addToCompositeCancellable(_compositeCancellable)
        .asFuture()
        .catchError((error) => throw error);
  }

  @override
  void clear() {
    _compositeCancellable.cancel();
  }
}
